#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
import json
from datetime import datetime
from database_config import get_database_path, get_database_connection
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtSql import *
from sub100_window import ConfirmationDialogs

# تضمين دالة إشعار الشهادات الطبية من print6.py
try:
    from print6 import print_medical_certificate_notification
    PRINT6_AVAILABLE = True
    print("تم استيراد دالة إشعار الشهادات الطبية من print6.py بنجاح")
except ImportError:
    PRINT6_AVAILABLE = False
    print("تحذير: فشل استيراد print_medical_certificate_notification من print6.py")
    # Define dummy function if import fails
    def print_medical_certificate_notification(*args, **kwargs):
        print("خطأ: دالة الطباعة print_medical_certificate_notification غير متوفرة.")
        return False

# تضمين دالة طباعة سجلات الدخول والتأخر من print2.py
try:
    from print2 import print_entry_records
    PRINT2_AVAILABLE = True
    print("تم استيراد دالة طباعة سجلات الدخول والتأخر من print2.py بنجاح")
except ImportError:
    PRINT2_AVAILABLE = False
    print("تحذير: فشل استيراد print_entry_records من print2.py")
    # Define dummy function if import fails
    def print_entry_records(*args, **kwargs):
        print("خطأ: دالة الطباعة print_entry_records غير متوفرة.")
        return False

class UniversalStudentRecordsWindow(QMainWindow):
    """نافذة عرض سجلات جميع التلاميذ - تستخدم منهجية Python + HTML الحديثة"""
    
    def __init__(self, record_type="entry_permissions", parent=None):
        # إنشاء النافذة بدون parent لضمان فتحها في كامل الشاشة
        super().__init__(None)

        # تعيين خصائص النافذة لضمان فتحها في كامل الشاشة
        self.setWindowFlags(Qt.Window | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)
        self.setAttribute(Qt.WA_DeleteOnClose, True)

        # المتغيرات الأساسية
        self.parent_window = parent
        self.record_type = record_type
        self.records_data = []
        self.filtered_data = []
        
        # إعدادات أنواع السجلات
        self.record_configs = {
            "entry_permissions": {
                "title": "🚪 سجل السماح بالدخول والتأخر",
                "table": "ورقة_السماح_بالدخول",
                "columns": [
                    ("rowid", "الرقم"),
                    ("التاريخ", "التاريخ"),
                    ("الرمز", "رمز التلميذ"),
                    ("الاسم_والنسب", "اسم التلميذ"),
                    ("الوقت", "الوقت"),
                    ("ورقة_السماح", "نوع السماح"),
                    ("السنة_الدراسية", "السنة الدراسية"),
                    ("الأسدس", "الأسدس"),
                    ("رقم_الورقة", "رقم الورقة")
                ],
                "filters": [
                    ("ورقة_السماح", "📋 نوع السماح"),
                    ("الأسدس", "� الأسدس")
                ],
                "stats": [
                    ("إجمالي السجلات", "len(data)"),
                    ("عدد التلاميذ", "len(set(row[3] for row in data if row[3]))"),
                    ("السماح بالدخول", "len([row for row in data if str(row[5]).strip() == 'سماح'])"),
                    ("السماح بتأخر", "len([row for row in data if str(row[5]).strip() == 'تأخر'])"),
                    ("الأسدس المختلفة", "len(set(row[7] for row in data if row[7]))")
                ]
            },
            "absence_records": {
                "title": "📝 سجل الغياب الأسبوعي",
                "table": "مسك_الغياب_الأسبوعي",
                "columns": [
                    ("rowid", "الرقم"),
                    ("التاريخ", "التاريخ"),
                    ("رمز_التلميذ", "رمز التلميذ"),
                    ("اسم_التلميذ", "اسم التلميذ"),
                    ("القسم", "القسم"),
                    ("المستوى", "المستوى"),
                    ("غياب_غير_مبرر", "غياب غير مبرر"),
                    ("السنة_الدراسية", "السنة الدراسية"),
                    ("الأسدس", "الأسدس")
                ],
                "filters": [
                    ("القسم", "📚 القسم"),
                    ("المستوى", "📖 المستوى")
                ],
                "stats": [
                    ("إجمالي السجلات", "len(data)"),
                    ("عدد التلاميذ", "len(set(row[3] for row in data if row[3]))"),
                    ("الأقسام", "len(set(row[4] for row in data if row[4]))"),
                    ("إجمالي أيام الغياب", "sum(int(row[6] or 0) for row in data)")
                ]
            },
            "doctor_visits": {
                "title": "🏥 سجل زيارات الطبيب",
                "table": "زيارة_الطبيب",
                "columns": [
                    ("rowid", "الرقم"),
                    ("التاريخ", "التاريخ"),
                    ("الرمز", "رمز التلميذ"),
                    ("الاسم_والنسب", "اسم التلميذ"),
                    ("القسم", "القسم"),
                    ("المستوى", "المستوى"),
                    ("السبب", "سبب الزيارة"),
                    ("الملاحظات", "ملاحظات"),
                    ("الوقت", "الوقت")
                ],
                "filters": [
                    ("القسم", "📚 القسم"),
                    ("المستوى", "📖 المستوى"),
                    ("السبب", "🩺 سبب الزيارة")
                ],
                "stats": [
                    ("إجمالي الزيارات", "len(data)"),
                    ("عدد التلاميذ", "len(set(row[3] for row in data if row[3]))"),
                    ("الأقسام", "len(set(row[4] for row in data if row[4]))"),
                    ("اليوم", "len([row for row in data if row[1] == datetime.now().strftime('%Y-%m-%d')])")
                ]
            },
            "student_cards": {
                "title": "👤 بطاقات التلاميذ",
                "table": "تلاميذ",
                "columns": [
                    ("rowid", "الرقم"),
                    ("الرمز", "رمز التلميذ"),
                    ("الاسم_واللقب", "اسم التلميذ"),
                    ("تاريخ_الازدياد", "تاريخ الازدياد"),
                    ("مكان_الازدياد", "مكان الازدياد"),
                    ("القسم", "القسم"),
                    ("المستوى", "المستوى"),
                    ("الجنس", "الجنس"),
                    ("رقم_الهاتف", "رقم الهاتف"),
                    ("العنوان", "العنوان")
                ],
                "filters": [
                    ("القسم", "📚 القسم"),
                    ("المستوى", "📖 المستوى"),
                    ("الجنس", "👫 الجنس")
                ],
                "stats": [
                    ("إجمالي التلاميذ", "len(data)"),
                    ("ذكور", "len([row for row in data if str(row[7]).strip() == 'ذكر'])"),
                    ("إناث", "len([row for row in data if str(row[7]).strip() == 'أنثى'])"),
                    ("الأقسام", "len(set(row[5] for row in data if row[5]))")
                ]
            }
        }
        
        # الحصول على إعدادات السجل الحالي
        self.current_config = self.record_configs.get(record_type, self.record_configs["entry_permissions"])
        
        # إعداد النافذة
        self.setupUI()

        # تحميل البيانات
        self.load_records_data()

        # التأكد من فتح النافذة في كامل الشاشة
        self.showMaximized()

        # إضافة timer لضمان فتح النافذة في كامل الشاشة بعد التحميل الكامل
        QTimer.singleShot(100, self.ensure_maximized)

    def showEvent(self, event):
        """التأكد من فتح النافذة في كامل الشاشة عند عرضها"""
        super().showEvent(event)

        # ضمان فتح النافذة في كامل الشاشة
        self.setWindowState(Qt.WindowMaximized)
        self.showMaximized()

        # إضافة timer إضافي للتأكد
        QTimer.singleShot(50, lambda: self.setWindowState(Qt.WindowMaximized))
        QTimer.singleShot(100, self.showMaximized)

    def resizeEvent(self, event):
        """منع تصغير النافذة والحفاظ على كامل الشاشة"""
        super().resizeEvent(event)
        # يمكن إضافة منطق إضافي هنا إذا لزم الأمر
    
    def setupUI(self):
        """إعداد واجهة المستخدم"""
        # إعدادات النافذة الأساسية
        self.setWindowTitle(f"{self.current_config['title']} ")
        self.setLayoutDirection(Qt.RightToLeft)

        # ضمان فتح النافذة في كامل الشاشة
        self.setWindowState(Qt.WindowMaximized)
        self.showMaximized()

        # إزالة أي قيود على الحجم
        self.setMinimumSize(0, 0)
        self.setMaximumSize(16777215, 16777215)
        
        # إضافة أيقونة للنافذة
        try:
            self.setWindowIcon(QIcon("01.ico"))
        except:
            pass
        
        # تطبيق نمط احترافي للنافذة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f0f4f8,
                    stop: 1 #e8f2f7
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # شريط الأدوات العلوي
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #f8f9fa,
                    stop: 1 #e9ecef
                );
                border: 1px solid #1976d2;
                border-radius: 6px;
                padding: 3px;
            }
        """)

        # تحديد ارتفاع أصغر لشريط الأدوات المحسن
        toolbar_frame.setFixedHeight(50)  # ارتفاع أصغر للصف الواحد
        toolbar_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        # تخطيط عمودي مع مساحات أقل
        toolbar_layout = QVBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(8, 5, 8, 5)
        toolbar_layout.setSpacing(3)

        # صف واحد: جميع عناصر التصفية والبحث
        filters_layout = QHBoxLayout()
        filters_layout.setSpacing(10)
        
        # شريط البحث
        search_label = QLabel("🔍")
        search_label.setFixedSize(30, 30)
        search_label.setStyleSheet("""
            color: #1565c0; 
            font-family: Calibri; 
            font-size: 16px; 
            font-weight: bold;
            padding: 0px;
            margin: 0px;
        """)
        filters_layout.addWidget(search_label)

        self.search_entry = QLineEdit()
        self.search_entry.setPlaceholderText("ابحث...")
        self.search_entry.setFixedHeight(30)
        self.search_entry.setStyleSheet("""
            QLineEdit {
                background: white;
                border: 1px solid #1565c0;
                border-radius: 4px;
                padding: 2px 6px;
                font-family: Calibri;
                font-size: 14px;
                color: black;
                min-width: 150px;
                max-width: 200px;
            }
            QLineEdit:focus {
                border-color: #0d47a1;
                border-width: 2px;
            }
        """)
        self.search_entry.textChanged.connect(self.on_search)
        filters_layout.addWidget(self.search_entry)
        
        # إضافة فاصل مرئي
        separator1 = QLabel("|")
        separator1.setStyleSheet("color: #ccc; font-size: 20px; margin: 0px 5px;")
        filters_layout.addWidget(separator1)
        
        # إنشاء جميع الفلاتر في نفس الصف
        self.filter_combos = {}
        for filter_field, filter_label in self.current_config["filters"]:
            # إنشاء تسمية الفلتر (مختصرة)
            label_text = filter_label.replace("📋 ", "").replace("📚 ", "").replace("📖 ", "").replace("� ", "").replace("🩺 ", "")
            label = QLabel(label_text + ":")
            label.setStyleSheet("""
                color: #1565c0; 
                font-family: Calibri; 
                font-size: 14px; 
                font-weight: bold;
                padding: 0px;
                margin: 0px;
            """)
            filters_layout.addWidget(label)

            # إنشاء مربع الفلتر
            combo = QComboBox()
            combo.setFixedHeight(30)
            combo.setStyleSheet("""
                QComboBox {
                    background: white;
                    border: 1px solid #1565c0;
                    border-radius: 4px;
                    padding: 2px 6px;
                    font-family: Calibri;
                    font-size: 14px;
                    color: black;
                    min-width: 100px;
                    max-width: 120px;
                }
                QComboBox:focus {
                    border-color: #0d47a1;
                    border-width: 2px;
                }
                QComboBox::drop-down {
                    border: none;
                    width: 16px;
                }
                QComboBox::down-arrow {
                    image: none;
                    border-left: 4px solid transparent;
                    border-right: 4px solid transparent;
                    border-top: 4px solid #1565c0;
                    margin-right: 4px;
                }
            """)
            combo.currentTextChanged.connect(self.on_filter)
            filters_layout.addWidget(combo)

            # حفظ مرجع الفلتر
            self.filter_combos[filter_field] = combo
        
        # إضافة فاصل مرئي آخر
        separator2 = QLabel("|")
        separator2.setStyleSheet("color: #ccc; font-size: 20px; margin: 0px 5px;")
        filters_layout.addWidget(separator2)
        
        # زر تحديث البيانات (مدمج)
        self.refresh_button = QPushButton("🔄")
        self.refresh_button.setFixedSize(35, 30)
        self.refresh_button.setToolTip("تحديث البيانات")
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-family: Calibri;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #5cbf60;
            }
            QPushButton:pressed {
                background: #3d8b40;
            }
        """)
        self.refresh_button.clicked.connect(self.load_records_data)
        filters_layout.addWidget(self.refresh_button)
        
        # إضافة مساحة مرنة لدفع العناصر لليسار
        filters_layout.addStretch()

        # إضافة الصف الواحد إلى التخطيط الرئيسي
        toolbar_layout.addLayout(filters_layout)

        main_layout.addWidget(toolbar_frame)
        
        # منطقة عرض HTML
        self.web_view = QWebEngineView()
        self.web_view.setStyleSheet("""
            QWebEngineView {
                border: 2px solid #1976d2;
                border-radius: 10px;
                background: white;
            }
        """)
        main_layout.addWidget(self.web_view)
        
        # شريط الأزرار السفلي
        buttons_frame = QFrame()
        buttons_frame.setFrameStyle(QFrame.StyledPanel)
        buttons_frame.setFixedHeight(80)  # تثبيت ارتفاع الشريط السفلي
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #37474f,
                    stop: 1 #263238
                );
                border-radius: 10px;
                padding: 5px;
                min-height: 80px;
                max-height: 80px;
            }
        """)
        
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(15, 10, 15, 10)
        buttons_layout.setSpacing(15)
        
        # زر طباعة التقرير
        self.print_button = QPushButton("📊 تقرير سجلات PDF")
        self.print_button.setFont(QFont("Calibri", 15, QFont.Bold))
        self.print_button.setMinimumHeight(40)
        self.print_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #FF9800,
                    stop: 1 #F57C00
                );
                color: black;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-family: Calibri;
                font-size: 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #FFB74D,
                    stop: 1 #FF9800
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #F57C00,
                    stop: 1 #E65100
                );
            }
            QPushButton:disabled {
                background: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.print_button.clicked.connect(self.print_report)
        buttons_layout.addWidget(self.print_button)
        
        # زر حذف السجلات المحددة
        self.delete_button = QPushButton("🗑️ حذف المحددة")
        self.delete_button.setFont(QFont("Calibri", 15, QFont.Bold))
        self.delete_button.setMinimumHeight(40)
        self.delete_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f44336,
                    stop: 1 #d32f2f
                );
                color: black;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-family: Calibri;
                font-size: 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f66356,
                    stop: 1 #f44336
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #c62828,
                    stop: 1 #b71c1c
                );
            }
        """)
        self.delete_button.clicked.connect(self.delete_selected_records)
        buttons_layout.addWidget(self.delete_button)
        
        # زر محو جميع التحديدات
        self.clear_selections_button = QPushButton("🧹 محو التحديدات")
        self.clear_selections_button.setFont(QFont("Calibri", 15, QFont.Bold))
        self.clear_selections_button.setMinimumHeight(40)
        self.clear_selections_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #6c757d,
                    stop: 1 #495057
                );
                color: black;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-family: Calibri;
                font-size: 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #868e96,
                    stop: 1 #6c757d
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #495057,
                    stop: 1 #343a40
                );
            }
        """)
        self.clear_selections_button.clicked.connect(self._clear_all_selections)
        buttons_layout.addWidget(self.clear_selections_button)
        
        buttons_layout.addStretch()
        
        main_layout.addWidget(buttons_frame)
        
        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #f5f5f5;
                border-top: 1px solid #ddd;
                font-size: 12px;
                color: #666;
                padding: 5px;
            }
        """)
        self.status_bar.showMessage("جاهز...")
    
    def load_records_data(self):
        """تحميل بيانات السجلات من قاعدة البيانات"""
        try:
            self.status_bar.showMessage("جاري تحميل البيانات...")
            
            conn = sqlite3.connect(get_database_path())
            cursor = conn.cursor()
            
            # بناء استعلام ديناميكي حسب نوع السجل
            table_name = self.current_config["table"]
            columns = [col[0] for col in self.current_config["columns"]]
            
            # التحقق من وجود الجدول أولاً
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if not cursor.fetchone():
                print(f"الجدول {table_name} غير موجود")
                self.status_bar.showMessage(f"الجدول {table_name} غير موجود")
                QMessageBox.warning(self, "تنبيه", f"الجدول {table_name} غير موجود في قاعدة البيانات")
                conn.close()
                return
            
            # التحقق من الأعمدة الموجودة في الجدول
            cursor.execute(f"PRAGMA table_info({table_name})")
            existing_columns = [column[1] for column in cursor.fetchall()]
            print(f"الأعمدة الموجودة في جدول {table_name}: {existing_columns}")
            
            # تصفية الأعمدة الموجودة فقط
            valid_columns = []
            for col in columns:
                if col == "rowid" or col in existing_columns:
                    valid_columns.append(col)
                else:
                    print(f"العمود {col} غير موجود في الجدول {table_name}")
            
            if not valid_columns:
                print(f"لا توجد أعمدة صالحة في الجدول {table_name}")
                self.status_bar.showMessage("لا توجد أعمدة صالحة")
                conn.close()
                return
            
            # تحديث تكوين الأعمدة مع الأعمدة الصالحة فقط
            original_columns = self.current_config["columns"]
            self.current_config["columns"] = [
                (col[0], col[1]) for col in original_columns 
                if col[0] == "rowid" or col[0] in existing_columns
            ]
            
            # تحميل جميع السجلات مع ترتيب آمن
            try:
                # محاولة الترتيب حسب التاريخ إذا كان موجوداً
                if "التاريخ" in valid_columns:
                    query = f"SELECT {', '.join(valid_columns)} FROM {table_name} ORDER BY التاريخ DESC"
                else:
                    query = f"SELECT {', '.join(valid_columns)} FROM {table_name} ORDER BY rowid DESC"
                
                cursor.execute(query)
                self.records_data = cursor.fetchall()
                print(f"تم تحميل {len(self.records_data)} سجل من جدول {table_name}")
                
            except Exception as e:
                print(f"خطأ في استعلام البيانات: {e}")
                # محاولة استعلام بسيط بدون ترتيب
                query = f"SELECT {', '.join(valid_columns)} FROM {table_name}"
                cursor.execute(query)
                self.records_data = cursor.fetchall()
            
            # تحميل قوائم الفلاتر
            for filter_field, _ in self.current_config["filters"]:
                if filter_field in self.filter_combos and filter_field in existing_columns:
                    # الفلاتر العادية الموجودة في الجدول الأساسي
                    try:
                        cursor.execute(f'SELECT DISTINCT {filter_field} FROM {table_name} WHERE {filter_field} IS NOT NULL AND {filter_field} != ""')
                        values = [row[0] for row in cursor.fetchall()]
                        
                        combo = self.filter_combos[filter_field]
                        combo.clear()
                        combo.addItems(['الكل'] + sorted([str(v) for v in values if v]))
                        print(f"تم تحميل {len(values)} عنصر للفلتر {filter_field}")
                    except Exception as e:
                        print(f"خطأ في تحميل فلتر {filter_field}: {e}")
            
            conn.close()
            
            # تطبيق الفلاتر وإنشاء HTML
            self.apply_filters()
            
            self.status_bar.showMessage(f"تم تحميل {len(self.records_data)} سجل")
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            self.status_bar.showMessage(f"خطأ في تحميل البيانات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات:\n{str(e)}")
    
    def apply_filters(self):
        """تطبيق الفلاتر على البيانات"""
        search_term = self.search_entry.text().lower()
        
        self.filtered_data = []
        for row in self.records_data:
            # تطبيق فلاتر مربعات الاختيار
            skip_row = False
            for i, (filter_field, _) in enumerate(self.current_config["filters"]):
                if filter_field in self.filter_combos:
                    filter_value = self.filter_combos[filter_field].currentText()
                    if filter_value != 'الكل':
                        # العثور على فهرس العمود في البيانات
                        column_names = [col[0] for col in self.current_config["columns"]]
                        if filter_field in column_names:
                            column_index = column_names.index(filter_field)
                            if column_index < len(row) and str(row[column_index]) != filter_value:
                                skip_row = True
                                break
            
            if skip_row:
                continue
                
            # تطبيق البحث النصي
            if search_term:
                row_text = ' '.join(str(cell or '') for cell in row).lower()
                if search_term not in row_text:
                    continue
                    
            self.filtered_data.append(row)
        
        # إنشاء وعرض HTML
        self.generate_html()
    
    def on_search(self):
        """معالج البحث"""
        self.apply_filters()
    
    def on_filter(self):
        """معالج التصفية"""
        self.apply_filters()
    
    def generate_html(self):
        """توليد HTML لعرض السجلات"""
        data_to_display = self.filtered_data
        html_content = self.create_html_template(data_to_display)
        self.web_view.setHtml(html_content)
    
    def create_html_template(self, data):
        """إنشاء قالب HTML حديث"""
        # حساب الإحصائيات
        stats_html = ""
        for stat_name, stat_formula in self.current_config["stats"]:
            try:
                stat_value = eval(stat_formula)
                stats_html += f"""
                <div class="stat-card">
                    <div class="stat-number">{stat_value}</div>
                    <div>{stat_name}</div>
                </div>
                """
            except:
                stats_html += f"""
                <div class="stat-card">
                    <div class="stat-number">0</div>
                    <div>{stat_name}</div>
                </div>
                """
        
        # إنشاء رؤوس الجدول
        table_headers = """
        <th>
            <input type="checkbox" id="select-all" onclick="toggleSelectAll()">
            اختيار الكل
        </th>
        """
        
        for _, column_title in self.current_config["columns"]:
            table_headers += f"<th>{column_title}</th>"
        
        # إنشاء صفوف الجدول
        table_rows = ""
        for row in data:
            row_html = '<tr class="record-row">'
            
            # إضافة مربع الاختيار
            row_html += f"""
            <td class="record-checkbox">
                <input type="checkbox" class="record-select" 
                       data-id="{row[0] or ''}"
                       data-student-name="{row[3] if len(row) > 3 else ''}"
                       data-date="{row[1] if len(row) > 1 else ''}">
            </td>
            """
            
            # إضافة بيانات الصف
            for i, cell in enumerate(row):
                if i == 0:  # العمود الأول (الرقم)
                    row_html += f'<td><span class="record-id">{cell or "غير محدد"}</span></td>'
                elif "تاريخ" in self.current_config["columns"][i][1].lower():
                    row_html += f'<td class="date">{cell or "غير محدد"}</td>'
                elif "اسم" in self.current_config["columns"][i][1].lower():
                    row_html += f'<td class="student-name">{cell or "غير محدد"}</td>'
                elif "سماح" in self.current_config["columns"][i][1].lower():
                    cell_value = str(cell or "غير محدد").strip()
                    if cell_value == "سماح":
                        row_html += f'<td><span class="permission-tag entry">{cell_value}</span></td>'
                    elif cell_value == "تأخر":
                        row_html += f'<td><span class="permission-tag late">{cell_value}</span></td>'
                    else:
                        row_html += f'<td>{cell_value}</td>'
                else:
                    row_html += f'<td>{cell or "غير محدد"}</td>'
            
            row_html += '</tr>'
            table_rows += row_html
        
        # HTML الكامل
        html = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{self.current_config['title']}</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }}
        
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
            min-height: 180px;
            max-height: 180px;
            height: 180px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }}
        
        .header h1 {{
            font-family: 'Calibri', sans-serif;
            font-size: 30px;
            font-weight: bold;
            color: #f8f9fa;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 20px;
            background: #f8f9fa;
        }}
        
        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }}
        
        .stat-card:hover {{
            transform: translateY(-5px);
        }}
        
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }}
        
        .table-container {{
            padding: 20px;
            overflow-x: auto;
        }}
        
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }}
        
        th {{
            background: linear-gradient(45deg, #2c3e50, #34495e);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }}
        
        td {{
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s ease;
        }}
        
        tr:hover td {{
            background-color: #f8f9fa;
        }}
        
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        
        .record-id {{
            background: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: bold;
        }}
        
        .student-name {{
            font-weight: bold;
            color: #2c3e50;
        }}
        
        .date {{
            color: #7f8c8d;
            font-size: 0.9em;
        }}
        
        .permission-tag {{
            padding: 4px 12px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 0.9em;
        }}
        
        .permission-tag.entry {{
            background: #27ae60;
            color: white;
        }}
        
        .permission-tag.late {{
            background: #e74c3c;
            color: white;
        }}
        
        .record-checkbox {{
            text-align: center;
            width: 60px;
        }}
        
        .record-select {{
            width: 18px;
            height: 18px;
            cursor: pointer;
            transform: scale(1.2);
        }}
        
        #select-all {{
            width: 18px;
            height: 18px;
            cursor: pointer;
            transform: scale(1.2);
        }}
        
        .record-row.selected {{
            background-color: #e3f2fd !important;
            border-right: 4px solid #2196f3;
        }}
        
        .empty-state {{
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }}
        
        .empty-icon {{
            font-size: 4em;
            margin-bottom: 20px;
            display: block;
            opacity: 0.5;
        }}
        
        @media (max-width: 768px) {{
            .header {{
                min-height: 160px;
                max-height: 160px;
                height: 160px;
                padding: 20px;
            }}
            
            .header h1 {{
                font-size: 24px;
            }}
        }}
    </style>
    
    <script>
        function toggleSelectAll() {{
            const selectAllBox = document.getElementById('select-all');
            const checkboxes = document.querySelectorAll('.record-select');
            
            checkboxes.forEach(checkbox => {{
                checkbox.checked = selectAllBox.checked;
                updateRowSelection(checkbox);
            }});
        }}
        
        function updateRowSelection(checkbox) {{
            const row = checkbox.closest('tr');
            if (checkbox.checked) {{
                row.classList.add('selected');
            }} else {{
                row.classList.remove('selected');
            }}
        }}
        
        function getSelectedRecords() {{
            const selectedCheckboxes = document.querySelectorAll('.record-select:checked');
            const selectedRecords = [];
            
            selectedCheckboxes.forEach(checkbox => {{
                selectedRecords.push({{
                    id: checkbox.getAttribute('data-id'),
                    studentName: checkbox.getAttribute('data-student-name'),
                    date: checkbox.getAttribute('data-date')
                }});
            }});
            
            return selectedRecords;
        }}
        
        document.addEventListener('DOMContentLoaded', function() {{
            const checkboxes = document.querySelectorAll('.record-select');
            checkboxes.forEach(checkbox => {{
                checkbox.addEventListener('change', function() {{
                    updateRowSelection(this);
                    
                    const allCheckboxes = document.querySelectorAll('.record-select');
                    const checkedBoxes = document.querySelectorAll('.record-select:checked');
                    const selectAllBox = document.getElementById('select-all');
                    
                    if (checkedBoxes.length === 0) {{
                        selectAllBox.indeterminate = false;
                        selectAllBox.checked = false;
                    }} else if (checkedBoxes.length === allCheckboxes.length) {{
                        selectAllBox.indeterminate = false;
                        selectAllBox.checked = true;
                    }} else {{
                        selectAllBox.indeterminate = true;
                    }}
                }});
            }});
        }});
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{self.current_config['title']}</h1>
            <p>تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
            <p style="font-size: 0.9em; opacity: 0.8;">📊 لإنشاء تقرير PDF: حدد السجلات الخاصة بتلميذ واحد فقط ثم اضغط على زر "تقرير سجلات PDF"</p>
        </div>
        
        <div class="stats">
            {stats_html}
        </div>
        
        <div class="table-container">
        """
        
        if data:
            html += f"""
            <table>
                <thead>
                    <tr>
                        {table_headers}
                    </tr>
                </thead>
                <tbody>
                    {table_rows}
                </tbody>
            </table>
            """
        else:
            html += """
            <div class="empty-state">
                <span class="empty-icon">📋</span>
                <h3>لا توجد سجلات</h3>
                <p>لم يتم العثور على أي سجلات تطابق معايير البحث</p>
            </div>
            """
        
        html += """
        </div>
    </div>
</body>
</html>
        """
        
        return html
    
    def delete_selected_records(self):
        """حذف السجلات المحددة"""
        try:
            # تأكيد الحذف
            reply = QMessageBox.question(
                self, 
                "تأكيد الحذف", 
                "هل أنت متأكد من حذف السجلات المحددة؟\n\n⚠️ هذا الإجراء لا يمكن التراجع عنه!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            # تنفيذ جافا سكريبت للحصول على السجلات المحددة
            js_code = """
            (function() {
                const selectedRecords = getSelectedRecords();
                return JSON.stringify(selectedRecords);
            })();
            """
            
            def handle_selected_records(result):
                try:
                    if not result:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي سجلات للحذف!")
                        return
                    
                    selected_records = json.loads(result)
                    if not selected_records:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي سجلات للحذف!")
                        return
                    
                    # حذف السجلات من قاعدة البيانات
                    conn = sqlite3.connect(get_database_path())
                    cursor = conn.cursor()
                    deleted_count = 0
                    
                    for record in selected_records:
                        # استخدام rowid بدلاً من id
                        cursor.execute(
                            f"DELETE FROM {self.current_config['table']} WHERE rowid = ?",
                            (record['id'],)
                        )
                        if cursor.rowcount > 0:
                            deleted_count += 1
                    
                    conn.commit()
                    conn.close()
                    
                    # إظهار نتيجة الحذف
                    if deleted_count > 0:
                        QMessageBox.information(
                            self, 
                            "نجح الحذف", 
                            f"تم حذف {deleted_count} سجل بنجاح! ✅"
                        )
                        # إعادة تحميل البيانات
                        self.load_records_data()
                        self.status_bar.showMessage(f"تم حذف {deleted_count} سجل")
                        
                        # محو جميع التحديدات بعد نجاح الحذف
                        print("🧹 محو التحديدات بعد نجاح عملية الحذف...")
                        QTimer.singleShot(500, self._clear_all_selections)  # تأخير قصير للسماح بإعادة التحميل
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في حذف السجلات!")
                        
                except Exception as e:
                    print(f"خطأ في معالجة السجلات المحددة: {e}")
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف:\n{str(e)}")
            
            # تنفيذ الجافا سكريبت
            self.web_view.page().runJavaScript(js_code, handle_selected_records)
            
        except Exception as e:
            print(f"خطأ في حذف السجلات: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف:\n{str(e)}")
    
    def print_report(self):
        """طباعة تقرير سجلات الدخول والتأخر لتلميذ محدد واحد فقط"""
        try:
            # التحقق من توفر دالة الطباعة
            if not PRINT2_AVAILABLE:
                QMessageBox.warning(
                    self, 
                    "خطأ", 
                    "دالة طباعة تقرير سجلات الدخول والتأخر غير متوفرة!\n\nتأكد من وجود ملف print2.py."
                )
                return

            # الحصول على السجلات المحددة من المستخدم
            js_code = """
            (function() {
                const selectedRecords = getSelectedRecords();
                return JSON.stringify(selectedRecords);
            })();
            """
            
            def handle_selected_records(result):
                try:
                    selected_records = []
                    if result:
                        selected_records = json.loads(result)
                    
                    # يجب على المستخدم تحديد سجلات تخص تلميذ واحد فقط
                    if not selected_records:
                        QMessageBox.information(
                            self, 
                            "تنبيه", 
                            "يرجى تحديد السجلات الخاصة بتلميذ واحد من الجدول لطباعة تقريره!\n\n"
                            "📝 كيفية الاستخدام:\n"
                            "1. حدد السجلات الخاصة بتلميذ واحد فقط\n"
                            "2. اضغط على زر 'تقرير سجلات PDF'\n"
                            "3. سيتم إنشاء تقرير واحد لذلك التلميذ"
                        )
                        return

                    # التحقق من أن جميع السجلات المحددة تخص نفس التلميذ
                    student_codes = set()
                    student_names = set()
                    
                    for record in selected_records:
                        student_name = record.get('studentName', '').strip()
                        if student_name:
                            student_names.add(student_name)
                    
                    # استخراج رموز التلاميذ من السجلات الفعلية
                    selected_ids = [record['id'] for record in selected_records]
                    target_data = [row for row in self.filtered_data if str(row[0]) in selected_ids]
                    
                    for row in target_data:
                        student_code = str(row[2]).strip() if len(row) > 2 else ""
                        if student_code and student_code != "غير محدد":
                            student_codes.add(student_code)

                    # التحقق من أن جميع السجلات تخص تلميذ واحد فقط
                    if len(student_codes) > 1 or len(student_names) > 1:
                        students_list = "\n".join([f"• {name}" for name in student_names if name])
                        QMessageBox.warning(
                            self, 
                            "تحديد خاطئ", 
                            f"لقد حددت سجلات لأكثر من تلميذ واحد!\n\n"
                            f"التلاميذ المحددون:\n{students_list}\n\n"
                            f"⚠️ يجب تحديد سجلات تلميذ واحد فقط لإنشاء تقريره.\n\n"
                            f"💡 اختر سجلات تلميذ واحد فقط ثم حاول مرة أخرى."
                        )
                        return
                    
                    if not student_codes or not student_names:
                        QMessageBox.warning(
                            self, 
                            "خطأ في البيانات", 
                            "لم يتم العثور على بيانات صحيحة للتلميذ في السجلات المحددة!\n\n"
                            "تأكد من تحديد سجلات صحيحة تحتوي على رمز واسم التلميذ."
                        )
                        return

                    # الحصول على بيانات التلميذ الواحد
                    student_code = list(student_codes)[0]
                    student_name = list(student_names)[0]
                    
                    print(f"🔄 بدء معالجة سجلات التلميذ: {student_name} (رمز: {student_code})")

                    # محاولة الحصول على المستوى والقسم من جدول اللوائح
                    level = "غير محدد"
                    class_name = "غير محدد"
                    
                    try:
                        temp_conn = sqlite3.connect(get_database_path())
                        temp_cursor = temp_conn.cursor()
                        
                        search_queries = [
                            "SELECT المستوى, القسم FROM اللوائح WHERE الرمز = ? LIMIT 1",
                            "SELECT المستوى, القسم FROM اللوائح WHERE رمز_التلميذ = ? LIMIT 1",
                            "SELECT level, class FROM اللوائح WHERE code = ? LIMIT 1",
                            "SELECT level, section FROM اللوائح WHERE student_code = ? LIMIT 1"
                        ]
                        
                        student_info = None
                        for query in search_queries:
                            try:
                                temp_cursor.execute(query, (student_code,))
                                student_info = temp_cursor.fetchone()
                                if student_info:
                                    level = str(student_info[0]).strip() if student_info[0] else "غير محدد"
                                    class_name = str(student_info[1]).strip() if student_info[1] else "غير محدد"
                                    print(f"   📋 تم العثور على بيانات التلميذ: المستوى={level}, القسم={class_name}")
                                    break
                            except Exception:
                                continue
                        
                        temp_conn.close()
                    except Exception as e:
                        print(f"   ❌ خطأ في البحث عن بيانات التلميذ: {e}")

                    # تجميع سجلات التلميذ
                    permission_records = []
                    late_records = []
                    all_records = []

                    for row in target_data:
                        # إنشاء بيانات السجل بالتنسيق المطلوب لـ print2.py
                        record_data = [
                            str(row[0]) if len(row) > 0 else "",  # رقم الورقة
                            str(row[1]) if len(row) > 1 else "",  # التاريخ
                            str(row[4]) if len(row) > 4 else "",  # الوقت
                            str(row[5]) if len(row) > 5 else "",  # نوع السماح
                            str(row[6]) if len(row) > 6 else "",  # السنة الدراسية
                            str(row[7]) if len(row) > 7 else "",  # الأسدس
                        ]
                        
                        # التحقق من وجود التاريخ والوقت
                        if not record_data[1] or not record_data[2]:
                            continue
                        
                        all_records.append(record_data)
                        
                        # تصنيف السجل حسب نوع السماح
                        permission_type = str(row[5]).strip() if len(row) > 5 else ""
                        
                        if permission_type == "سماح":
                            permission_records.append(record_data)
                        elif permission_type == "تأخر":
                            late_records.append(record_data)
                        else:
                            permission_records.append(record_data)

                    if not all_records:
                        QMessageBox.warning(
                            self, 
                            "لا توجد سجلات صالحة", 
                            f"لا توجد سجلات صالحة للتلميذ {student_name}!\n\n"
                            "تأكد من أن السجلات تحتوي على تواريخ وأوقات صحيحة."
                        )
                        return

                    # إعداد بيانات التلميذ للطباعة
                    student_data = {
                        'student_name': student_name,
                        'student_code': student_code,
                        'level': level,
                        'class': class_name,
                        'permission_records': permission_records,
                        'late_records': late_records,
                        'all_records': all_records
                    }

                    # جمع بيانات المؤسسة
                    institution_data = {}
                    try:
                        conn = sqlite3.connect(get_database_path())
                        cursor = conn.cursor()
                        cursor.execute("SELECT المؤسسة, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
                        result = cursor.fetchone()
                        if result:
                            institution_data['institution'] = result[0]
                            institution_data['school_year'] = result[1]
                        else:
                            institution_data['institution'] = 'مؤسسة تعليمية'
                            institution_data['school_year'] = '2024/2025'
                        conn.close()
                    except Exception as e:
                        print(f"خطأ في استخراج بيانات المؤسسة: {e}")
                        institution_data['institution'] = 'مؤسسة تعليمية'
                        institution_data['school_year'] = '2024/2025'

                    # إنشاء مجلد التقارير
                    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                    main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
                    reports_dir = os.path.join(main_reports_dir, "تقارير سجلات الدخول والتأخر")
                    
                    if not os.path.exists(reports_dir):
                        os.makedirs(reports_dir)

                    # إنشاء اسم ملف فريد
                    from datetime import datetime
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    safe_student_name = ''.join(c if c.isalnum() or c in '_-' else '_' for c in student_name)
                    safe_student_name = '_'.join(filter(None, safe_student_name.split('_')))
                    if len(safe_student_name) > 30:
                        safe_student_name = safe_student_name[:30]
                    safe_student_code = ''.join(c if c.isalnum() or c in '_-' else '_' for c in str(student_code))
                    
                    file_name = f"تقرير_سجلات_{safe_student_name}_رمز_{safe_student_code}_{timestamp}"
                    
                    institution_data['output_dir'] = reports_dir
                    institution_data['file_name'] = file_name

                    # طباعة معلومات التقرير
                    print(f"📄 إنشاء تقرير للتلميذ: {student_name} (رمز: {student_code})")
                    print(f"   📚 المستوى: {level} - القسم: {class_name}")
                    print(f"   📊 عدد السجلات: {len(all_records)} (سماح: {len(permission_records)}, تأخر: {len(late_records)})")
                    print(f"   📁 اسم الملف: {file_name}.pdf")

                    # إنشاء التقرير
                    try:
                        self.status_bar.showMessage(f"جاري إنشاء تقرير التلميذ {student_name}...")
                        success = print_entry_records(institution_data, student_data)
                        
                        if success:
                            QMessageBox.information(
                                self, 
                                "تم بنجاح ✅", 
                                f"تم إنشاء تقرير التلميذ بنجاح!\n\n"
                                f"👤 التلميذ: {student_name}\n"
                                f"🔢 الرمز: {student_code}\n"
                                f"📚 المستوى: {level} - القسم: {class_name}\n\n"
                                f"📊 إحصائيات التقرير:\n"
                                f"• إجمالي السجلات: {len(all_records)}\n"
                                f"• سجلات السماح: {len(permission_records)}\n"
                                f"• سجلات التأخر: {len(late_records)}\n\n"
                                f"📁 تم حفظ التقرير في:\n{reports_dir}"
                            )
                            self.status_bar.showMessage(f"تم إنشاء تقرير التلميذ {student_name} بنجاح ✅")
                            
                            # محو التحديدات بعد نجاح الطباعة
                            self._clear_all_selections()
                            
                            # فتح مجلد التقارير
                            try:
                                import subprocess
                                subprocess.Popen(f'explorer "{reports_dir}"')
                            except Exception:
                                pass
                        else:
                            QMessageBox.warning(
                                self, 
                                "خطأ في الطباعة ❌", 
                                f"فشل في إنشاء تقرير التلميذ {student_name}!\n\n"
                                "تحقق من:\n"
                                "• وجود ملف print2.py\n"
                                "• صلاحيات كتابة الملفات\n"
                                "• تثبيت مكتبة reportlab"
                            )
                    except Exception as e:
                        print(f"💥 خطأ في إنشاء التقرير: {e}")
                        QMessageBox.critical(
                            self, 
                            "خطأ في الطباعة", 
                            f"حدث خطأ أثناء إنشاء تقرير التلميذ {student_name}:\n\n{str(e)}"
                        )
                        
                except Exception as e:
                    print(f"خطأ في معالجة السجلات المحددة: {e}")
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء معالجة السجلات:\n{str(e)}")
            
            # تنفيذ الجافا سكريبت للحصول على السجلات المحددة
            self.web_view.page().runJavaScript(js_code, handle_selected_records)
            
        except Exception as e:
            print(f"خطأ في طباعة التقرير: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في طباعة التقرير:\n{str(e)}")
    
    def _clear_all_selections(self):
        """محو جميع التحديدات في الجدول"""
        try:
            print("🧹 بدء محو جميع التحديدات...")
            
            # JavaScript لمحو جميع التحديدات
            js_code = """
            (function() {
                try {
                    // إلغاء تحديد مربع "اختيار الكل"
                    const selectAllBox = document.getElementById('select-all');
                    if (selectAllBox) {
                        selectAllBox.checked = false;
                        selectAllBox.indeterminate = false;
                    }
                    
                    // إلغاء تحديد جميع مربعات التحديد الفردية
                    const checkboxes = document.querySelectorAll('.record-select');
                    let clearedCount = 0;
                    
                    checkboxes.forEach(checkbox => {
                        if (checkbox.checked) {
                            checkbox.checked = false;
                            clearedCount++;
                            
                            // إزالة التأثير المرئي للتحديد
                            const row = checkbox.closest('tr');
                            if (row) {
                                row.classList.remove('selected');
                            }
                        }
                    });
                    
                    return {
                        success: true,
                        clearedCount: clearedCount,
                        totalCheckboxes: checkboxes.length
                    };
                    
                } catch (error) {
                    return {
                        success: false,
                        error: error.message
                    };
                }
            })();
            """
            
            # تنفيذ الجافا سكريبت
            def handle_clear_result(result):
                try:
                    if result:
                        import json
                        clear_data = json.loads(result)
                        
                        if clear_data.get('success', False):
                            cleared_count = clear_data.get('clearedCount', 0)
                            total_checkboxes = clear_data.get('totalCheckboxes', 0)
                            print(f"✅ تم محو {cleared_count} تحديد من أصل {total_checkboxes} مربع تحديد")
                            
                            # تحديث شريط الحالة
                            if cleared_count > 0:
                                self.status_bar.showMessage(f"تم محو {cleared_count} تحديد ✅")
                            else:
                                self.status_bar.showMessage("لا توجد تحديدات لمحوها ℹ️")
                        else:
                            error_msg = clear_data.get('error', 'خطأ غير معروف')
                            print(f"❌ فشل في محو التحديدات: {error_msg}")
                            self.status_bar.showMessage("فشل في محو التحديدات ❌")
                    else:
                        print("⚠️ لم يتم إرجاع نتيجة من JavaScript")
                        
                except Exception as e:
                    print(f"❌ خطأ في معالجة نتيجة محو التحديدات: {e}")
            
            # تنفيذ الجافا سكريبت مع معالج النتيجة
            self.web_view.page().runJavaScript(js_code, handle_clear_result)
            
        except Exception as e:
            print(f"❌ خطأ في دالة محو التحديدات: {e}")
    
    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        # إيقاف مراقبة النافذة
        if hasattr(self, 'window_monitor_timer'):
            self.window_monitor_timer.stop()
        event.accept()

    def ensure_maximized(self):
        """دالة مساعدة لضمان فتح النافذة في كامل الشاشة"""
        # إزالة أي قيود على الحجم
        self.setMinimumSize(0, 0)
        self.setMaximumSize(16777215, 16777215)

        # تعيين حالة النافذة
        self.setWindowState(Qt.WindowMaximized)

        # إظهار النافذة في كامل الشاشة
        self.showMaximized()

        # تفعيل النافذة ورفعها للمقدمة
        self.activateWindow()
        self.raise_()

        # التأكد من التركيز
        self.setFocus()

        # بدء مراقبة حالة النافذة
        # بدء مراقبة حالة النافذة
        self.start_window_monitor()

    def start_window_monitor(self):
        """بدء مراقبة حالة النافذة للتأكد من بقائها في كامل الشاشة"""
        self.window_monitor_timer = QTimer()
        self.window_monitor_timer.timeout.connect(self.check_window_state)
        self.window_monitor_timer.start(1000)  # فحص كل ثانية

    def check_window_state(self):
        """فحص حالة النافذة والتأكد من أنها في كامل الشاشة"""
        if self.windowState() != Qt.WindowMaximized:
            self.setWindowState(Qt.WindowMaximized)
            self.showMaximized()


# للتوافق مع الكود القديم
class StudentRecordsWindow(UniversalStudentRecordsWindow):
    """نافذة سجلات التلميذ - للتوافق مع الكود القديم"""
    
    def __init__(self, student_code=None, student_name=None, db=None, parent=None):
        # تحويل للنافذة العامة مع فلتر التلميذ
        super().__init__("entry_permissions", parent)
        
        # إضافة فلتر للتلميذ المحدد إذا كان موجوداً
        if student_code:
            # يمكن إضافة منطق لفلترة البيانات حسب رمز التلميذ
            self.student_code = student_code
            self.student_name = student_name or "غير محدد"


def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # إنشاء النافذة
    window = UniversalStudentRecordsWindow(record_type="entry_permissions")

    # ضمان فتح النافذة في كامل الشاشة بطرق متعددة
    window.setWindowState(Qt.WindowMaximized)
    window.showMaximized()
    window.ensure_maximized()

    # إضافة timer للتأكد من فتح النافذة في كامل الشاشة بعد بدء التطبيق
    QTimer.singleShot(200, window.ensure_maximized)
    QTimer.singleShot(500, window.ensure_maximized)

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
